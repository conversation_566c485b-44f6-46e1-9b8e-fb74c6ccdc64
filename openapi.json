{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/docs/tag/{tag}": {"get": {"operationId": "core_api_docs_get_docs_by_tag", "summary": "Get API documentation for a specific tag", "parameters": [{"in": "path", "name": "tag", "schema": {"title": "Tag", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Get OpenAPI documentation for all endpoints with a specific tag,\nincluding the request/response schemas they use.", "tags": ["docs"]}}, "/api/accounts/": {"post": {"operationId": "accounts_api_login", "summary": "<PERSON><PERSON>", "parameters": [{"in": "query", "name": "phone", "schema": {"title": "Phone", "type": "string"}, "required": true}, {"in": "query", "name": "password", "schema": {"title": "Password", "type": "string"}, "required": true}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginOutSchema"}}}}}, "description": "Login to the system and retrun token and user info", "tags": ["accounts"]}}, "/api/accounts/me": {"get": {"operationId": "accounts_api_get_me", "summary": "Get Me", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "description": "Get the current user", "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/create": {"post": {"operationId": "accounts_api_create_user", "summary": "Create User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/edit/{user_id}": {"put": {"operationId": "accounts_api_edit_user", "summary": "Edit User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/delete/{user_id}": {"delete": {"operationId": "accounts_api_delete_user", "summary": "Delete User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}, "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/update-location": {"post": {"operationId": "accounts_api_update_location", "summary": "Update Location", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "description": "Update the current user's location coordinates", "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationUpdateSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/employees/{employee_id}/stats/": {"get": {"operationId": "accounts_api_get_employee_stats", "summary": "Get Employee Stats", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeStatsResponseSchema"}}}}}, "description": "Get statistics for an employee within a specific date range", "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeStatsQuerySchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/": {"get": {"operationId": "orders_api_get_order", "summary": "Get Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchemaWithHistory"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}, "put": {"operationId": "orders_api_edit_order", "summary": "Edit Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_api_delete_order", "summary": "Delete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"s": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "u": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "c": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "e": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders": {"get": {"operationId": "orders_api_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "date_from", "schema": {"title": "Date From", "type": "string"}, "required": false}, {"in": "query", "name": "date_to", "schema": {"title": "Date To", "type": "string"}, "required": false}, {"in": "query", "name": "status", "schema": {"allOf": [{"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatus", "type": "string"}]}, "required": false}, {"in": "query", "name": "assigned_to", "schema": {"title": "Assigned To", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders/": {"post": {"operationId": "orders_api_add_order", "summary": "Add Order", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/assign/": {"post": {"operationId": "orders_api_assign_order", "summary": "Assign Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAssignSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/transfer/": {"post": {"operationId": "orders_api_transfer_order", "summary": "Transfer Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderTransferSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/proof/": {"post": {"operationId": "orders_api_add_proof", "summary": "Add Proof", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderProofOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"multipart/form-data": {"schema": {"title": "MultiPartBodyParams", "type": "object", "properties": {"proof_type": {"title": "Proof Type", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}, "img": {"format": "binary", "title": "Img", "type": "string"}}, "required": ["proof_type"]}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/me/": {"get": {"operationId": "orders_api_list_my_orders", "summary": "List My Orders", "parameters": [{"in": "query", "name": "date_from", "schema": {"title": "Date From", "type": "string"}, "required": false}, {"in": "query", "name": "date_to", "schema": {"title": "Date To", "type": "string"}, "required": false}, {"in": "query", "name": "status", "schema": {"title": "Status", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/complete/": {"post": {"operationId": "orders_api_complete_order", "summary": "Complete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCompleteSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders_statuses": {"get": {"operationId": "orders_api_list_order_statuses", "summary": "List Order Statuses", "parameters": [{"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderStatusOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/companies": {"get": {"operationId": "orders_api_list_companies", "summary": "List Companies", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CompanyOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["companies"], "security": [{"AuthBearer": []}]}}, "/api/companies/": {"post": {"operationId": "orders_api_add_company", "summary": "Add Company", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOutSchema"}}}}}, "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/companies/{company_id}/": {"put": {"operationId": "orders_api_edit_company", "summary": "Edit Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOutSchema"}}}}}, "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_api_delete_company", "summary": "Delete Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}, "tags": ["companies"], "security": [{"AuthBearer": []}]}}, "/api/offices/{office_id}/": {"put": {"operationId": "offices_api_edit_office", "summary": "Edit Office", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeOutSchema"}}}}}, "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/offices/users": {"get": {"operationId": "offices_api_list_office_users", "summary": "List Office Users", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserModelSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all users who belong to the same office.", "tags": ["offices"], "security": [{"AuthBearer": []}]}}, "/api/offices/performance/": {"post": {"operationId": "offices_api_get_office_performance", "summary": "Get Office Performance", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficePerformanceSchema"}}}}}, "description": "Get performance report for the entire office.", "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateRangeSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/offices/employees/{employee_id}/performance/": {"post": {"operationId": "offices_api_get_employee_performance", "summary": "Get Employee Performance", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeePerformanceSchema"}}}}}, "description": "Get performance report for a specific employee.", "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateRangeSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}}, "components": {"schemas": {"LoginOutSchema": {"properties": {"token": {"title": "Token", "type": "string"}, "user": {"$ref": "#/components/schemas/UserModelSchema"}}, "required": ["token", "user"], "title": "LoginOutSchema", "type": "object"}, "Office": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["name", "created_at", "updated_at"], "title": "Office", "type": "object"}, "RoleEnum": {"enum": ["MASTER", "MANAGER", "EMPLOYEE", "CUSTOM_USER"], "title": "RoleEnum", "type": "string"}, "UserModelSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "username": {"description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "maxLength": 150, "title": "Username", "type": "string"}, "first_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "First Name"}, "last_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "Last Name"}, "email": {"anyOf": [{"format": "email", "type": "string"}, {"type": "null"}], "description": "", "title": "Email Address"}, "date_joined": {"description": "", "format": "date-time", "title": "Date Joined", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/RoleEnum"}], "default": "EMPLOYEE", "description": "", "title": "Role"}, "office": {"anyOf": [{"$ref": "#/components/schemas/Office"}, {"type": "null"}], "description": "", "title": "Office"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Commission rate percentage for delivery employees", "title": "Commission Rate"}, "current_location_lat": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lat"}, "current_location_lng": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lng"}, "last_location_update": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Location Update"}}, "required": ["username"], "title": "UserModelSchema", "type": "object"}, "Role": {"enum": ["MASTER", "MANAGER", "EMPLOYEE", "CUSTOM_USER"], "title": "Role", "type": "string"}, "UserCreateSchema": {"properties": {"username": {"title": "Username", "type": "string"}, "password": {"title": "Password", "type": "string"}, "first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/Role"}], "default": "EMPLOYEE"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Rate"}}, "required": ["username", "password", "phone"], "title": "UserCreateSchema", "type": "object"}, "UserEditSchema": {"properties": {"username": {"minLength": 3, "title": "Username", "type": "string"}, "first_name": {"minLength": 3, "title": "First Name", "type": "string"}, "last_name": {"minLength": 3, "title": "Last Name", "type": "string"}, "email": {"format": "email", "title": "Email", "type": "string"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/Role"}]}, "commission_rate": {"maximum": 100, "minimum": 0, "title": "Commission Rate", "type": "number"}}, "title": "UserEditSchema", "type": "object"}, "SuccessResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}}, "required": ["success"], "title": "SuccessResponse", "type": "object"}, "LocationUpdateSchema": {"properties": {"lat": {"title": "Lat", "type": "number"}, "lng": {"title": "Lng", "type": "number"}}, "required": ["lat", "lng"], "title": "LocationUpdateSchema", "type": "object"}, "EmployeeStatsResponseSchema": {"properties": {"total_money_deserved": {"title": "Total Money Deserved", "type": "number"}, "total_orders_completed": {"title": "Total Orders Completed", "type": "integer"}, "total_orders_assigned": {"title": "Total Orders Assigned", "type": "integer"}, "total_money_to_collect": {"title": "Total Money To Collect", "type": "number"}, "total_money_collected": {"title": "Total Money Collected", "type": "number"}}, "required": ["total_money_deserved", "total_orders_completed", "total_orders_assigned", "total_money_to_collect", "total_money_collected"], "title": "EmployeeStatsResponseSchema", "type": "object"}, "EmployeeStatsQuerySchema": {"properties": {"date_from": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Date From"}, "date_to": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Date To"}}, "title": "EmployeeStatsQuerySchema", "type": "object"}, "Company": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"description": "", "title": "Office", "type": "integer"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "color_code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "Hex color code for the company", "title": "Color Code"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "name", "created_at", "updated_at"], "title": "Company", "type": "object"}, "HandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "HandlingStatusEnum", "type": "string"}, "Order": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"description": "", "title": "Office", "type": "integer"}, "code": {"description": "", "maxLength": 255, "title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount customer should pay", "title": "Total Price"}, "customer_name": {"description": "", "maxLength": 255, "title": "Customer Name", "type": "string"}, "customer_phone": {"description": "List of customer phone numbers separated by `-`", "title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Customer Address"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Special commission rate for the order", "title": "Special Commission Rate"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Delivery Deadline Date"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount paid by the customer", "title": "Delivery Customer Payment"}, "order_handling_status": {"allOf": [{"$ref": "#/components/schemas/OrderHandlingStatusEnum"}], "default": "PENDING", "description": "", "title": "Order Handling Status"}, "order_delivery_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Order Delivery Status"}, "assigned_to": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Assigned To"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Assigned At"}, "customer_company": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Customer Company"}, "breakable": {"default": false, "description": "Whether the order contains breakable items like glass", "title": "Breakable", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "code", "customer_name", "customer_phone", "created_at", "updated_at"], "title": "Order", "type": "object"}, "OrderAssignmentHistoryOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "assigned_to": {"$ref": "#/components/schemas/User", "description": "", "title": "Assigned To"}, "assigned_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Assigned By"}, "assigned_at": {"description": "", "format": "date-time", "title": "Assigned At", "type": "string"}}, "required": ["office", "order", "assigned_to", "assigned_by", "assigned_at"], "title": "OrderAssignmentHistoryOutSchema", "type": "object"}, "OrderDefaultHandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderDefaultHandlingStatusEnum", "type": "string"}, "OrderDeliveryStatus": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Office"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Description"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDefaultHandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Order Default Handling Status"}}, "required": ["name", "created_at", "updated_at"], "title": "OrderDeliveryStatus", "type": "object"}, "OrderHandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatusEnum", "type": "string"}, "OrderHandlingStatusHistoryOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "handling_status": {"anyOf": [{"$ref": "#/components/schemas/HandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Handling Status"}, "delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Delivery Status"}, "changed_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Changed By"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Note"}, "proof": {"anyOf": [{"$ref": "#/components/schemas/OrderProof"}, {"type": "null"}], "description": "", "title": "Proof"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "order", "changed_by", "created_at", "updated_at"], "title": "OrderHandlingStatusHistoryOutSchema", "type": "object"}, "OrderOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "code": {"description": "", "maxLength": 255, "title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount customer should pay", "title": "Total Price"}, "customer_name": {"description": "", "maxLength": 255, "title": "Customer Name", "type": "string"}, "customer_phone": {"description": "List of customer phone numbers separated by `-`", "title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Customer Address"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Special commission rate for the order", "title": "Special Commission Rate"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Delivery Deadline Date"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount paid by the customer", "title": "Delivery Customer Payment"}, "order_handling_status": {"allOf": [{"$ref": "#/components/schemas/OrderHandlingStatusEnum"}], "default": "PENDING", "description": "", "title": "Order Handling Status"}, "order_delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Order Delivery Status"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "null"}], "description": "", "title": "Assigned To"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Assigned At"}, "customer_company": {"anyOf": [{"$ref": "#/components/schemas/Company"}, {"type": "null"}], "description": "", "title": "Customer Company"}, "breakable": {"default": false, "description": "Whether the order contains breakable items like glass", "title": "Breakable", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "code", "customer_name", "customer_phone", "created_at", "updated_at"], "title": "OrderOutSchema", "type": "object"}, "OrderOutSchemaWithHistory": {"properties": {"order": {"$ref": "#/components/schemas/OrderOutSchema"}, "assigning_history": {"items": {"$ref": "#/components/schemas/OrderAssignmentHistoryOutSchema"}, "title": "Assigning History", "type": "array"}, "handling_status_history": {"items": {"$ref": "#/components/schemas/OrderHandlingStatusHistoryOutSchema"}, "title": "Handling Status History", "type": "array"}, "proofs": {"items": {"$ref": "#/components/schemas/OrderProofOutSchema"}, "title": "Proofs", "type": "array"}}, "required": ["order", "assigning_history", "handling_status_history", "proofs"], "title": "OrderOutSchemaWithHistory", "type": "object"}, "OrderProof": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "order": {"description": "", "title": "Order", "type": "integer"}, "proof_by": {"description": "", "title": "Proof By", "type": "integer"}, "proof_type": {"$ref": "#/components/schemas/ProofTypeEnum", "description": "", "title": "Proof Type"}, "proof_img": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Proof Img"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Longitude"}}, "required": ["order", "proof_by", "proof_type", "created_at", "updated_at"], "title": "OrderProof", "type": "object"}, "OrderProofOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "proof_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Proof By"}, "proof_type": {"$ref": "#/components/schemas/ProofTypeEnum", "description": "", "title": "Proof Type"}, "proof_img": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Proof Img"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Longitude"}}, "required": ["order", "proof_by", "proof_type", "created_at", "updated_at"], "title": "OrderProofOutSchema", "type": "object"}, "ProofTypeEnum": {"enum": ["PROOF_OF_ASSIGNMENT", "PROOF_OF_DELIVERY", "PROOF_OF_RETURN"], "title": "ProofTypeEnum", "type": "string"}, "User": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "password": {"description": "", "maxLength": 128, "title": "Password", "type": "string"}, "last_login": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Login"}, "is_superuser": {"default": false, "description": "Designates that this user has all permissions without explicitly assigning them.", "title": "Superuser Status", "type": "boolean"}, "username": {"description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "maxLength": 150, "title": "Username", "type": "string"}, "first_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "First Name"}, "last_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "Last Name"}, "email": {"anyOf": [{"format": "email", "type": "string"}, {"type": "null"}], "description": "", "title": "Email Address"}, "is_staff": {"default": false, "description": "Designates whether the user can log into this admin site.", "title": "Staff Status", "type": "boolean"}, "date_joined": {"description": "", "format": "date-time", "title": "Date Joined", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/RoleEnum"}], "default": "EMPLOYEE", "description": "", "title": "Role"}, "office": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Office"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Commission rate percentage for delivery employees", "title": "Commission Rate"}, "current_location_lat": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lat"}, "current_location_lng": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lng"}, "last_location_update": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Location Update"}, "groups": {"description": "The groups this user belongs to. A user will get all permissions granted to each of their groups.", "items": {"type": "integer"}, "title": "Groups", "type": "array"}, "user_permissions": {"description": "Specific permissions for this user.", "items": {"type": "integer"}, "title": "User Permissions", "type": "array"}}, "required": ["password", "username", "groups", "user_permissions"], "title": "User", "type": "object"}, "OrderInSchema": {"properties": {"code": {"title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_name": {"title": "Customer Name", "type": "string"}, "customer_phone": {"title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"minLength": 3, "type": "string"}, {"type": "null"}], "title": "Customer Address"}, "customer_company": {"title": "Customer Company", "type": "integer"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Delivery Deadline Date"}, "order_delivery_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Delivery Status"}, "breakable": {"default": false, "title": "Breakable", "type": "boolean"}}, "required": ["code", "customer_name", "customer_phone", "customer_company"], "title": "OrderInSchema", "type": "object"}, "OrderHandlingStatus": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatus", "type": "string"}, "OrderAssignSchema": {"properties": {"employee_id": {"title": "Employee Id", "type": "integer"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Special Commission Rate"}}, "required": ["employee_id"], "title": "OrderAssignSchema", "type": "object"}, "OrderTransferSchema": {"properties": {"to_employee_id": {"title": "To Employee Id", "type": "integer"}, "proof_type": {"title": "Proof Type", "type": "string"}, "proof_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Proof Url"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}}, "required": ["to_employee_id", "proof_type"], "title": "OrderTransferSchema", "type": "object"}, "OrderProofInSchema": {"properties": {"proof_type": {"title": "Proof Type", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}}, "required": ["proof_type"], "title": "OrderProofInSchema", "type": "object"}, "OrderCompleteSchema": {"properties": {"proof_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Proof Type"}, "proof_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Proof Url"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Delivery Customer Payment"}, "order_delivery_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Delivery Status"}}, "title": "OrderCompleteSchema", "type": "object"}, "OrderStatusOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Office"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Description"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDefaultHandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Order Default Handling Status"}}, "required": ["name", "created_at", "updated_at"], "title": "OrderStatusOutSchema", "type": "object"}, "CompanyOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "color_code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "Hex color code for the company", "title": "Color Code"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "name", "created_at", "updated_at"], "title": "CompanyOutSchema", "type": "object"}, "CompanyInSchema": {"properties": {"name": {"minLength": 3, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "color_code": {"minLength": 7, "pattern": "^#[0-9A-Fa-f]{6}$", "title": "Color Code", "type": "string"}}, "required": ["name", "phone", "color_code"], "title": "CompanyInSchema", "type": "object"}, "CompanyEditSchema": {"properties": {"name": {"anyOf": [{"minLength": 3, "type": "string"}, {"type": "null"}], "title": "Name"}, "address": {"anyOf": [{"minLength": 10, "type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"anyOf": [{"minLength": 10, "type": "string"}, {"type": "null"}], "title": "Phone"}, "color_code": {"anyOf": [{"minLength": 7, "pattern": "^#[0-9A-Fa-f]{6}$", "type": "string"}, {"type": "null"}], "title": "Color Code"}}, "title": "CompanyEditSchema", "type": "object"}, "OfficeOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["name", "created_at", "updated_at"], "title": "OfficeOutSchema", "type": "object"}, "OfficeEditSchema": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "title": "OfficeEditSchema", "type": "object"}, "OfficePerformanceSchema": {"properties": {"total_orders": {"title": "Total Orders", "type": "integer"}, "orders_by_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Status", "type": "object"}, "orders_by_delivery_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Delivery Status", "type": "object"}, "total_revenue": {"title": "Total Revenue", "type": "number"}, "average_order_value": {"title": "Average Order Value", "type": "number"}, "collection_rate": {"title": "Collection Rate", "type": "number"}, "average_completion_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Completion Time"}, "on_time_delivery_rate": {"title": "On Time Delivery Rate", "type": "number"}, "orders_by_company": {"items": {"additionalProperties": true, "type": "object"}, "title": "Orders By Company", "type": "array"}, "top_performing_employees": {"items": {"additionalProperties": true, "type": "object"}, "title": "Top Performing Employees", "type": "array"}, "daily_performance": {"items": {"additionalProperties": true, "type": "object"}, "title": "Daily Performance", "type": "array"}}, "required": ["total_orders", "orders_by_status", "orders_by_delivery_status", "total_revenue", "average_order_value", "collection_rate", "on_time_delivery_rate", "orders_by_company", "top_performing_employees", "daily_performance"], "title": "OfficePerformanceSchema", "type": "object"}, "DateRangeSchema": {"properties": {"start_date": {"format": "date", "title": "Start Date", "type": "string"}, "end_date": {"format": "date", "title": "End Date", "type": "string"}}, "required": ["start_date", "end_date"], "title": "DateRangeSchema", "type": "object"}, "EmployeePerformanceSchema": {"properties": {"employee_id": {"title": "Employee Id", "type": "integer"}, "employee_name": {"title": "Employee Name", "type": "string"}, "total_orders_assigned": {"title": "Total Orders Assigned", "type": "integer"}, "total_orders_completed": {"title": "Total Orders Completed", "type": "integer"}, "completion_rate": {"title": "Completion Rate", "type": "number"}, "average_completion_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Completion Time"}, "total_revenue": {"title": "Total Revenue", "type": "number"}, "commission_earned": {"title": "Commission Earned", "type": "number"}, "average_order_value": {"title": "Average Order Value", "type": "number"}, "on_time_delivery_percentage": {"title": "On Time Delivery Percentage", "type": "number"}, "orders_by_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Status", "type": "object"}, "daily_performance": {"items": {"additionalProperties": true, "type": "object"}, "title": "Daily Performance", "type": "array"}}, "required": ["employee_id", "employee_name", "total_orders_assigned", "total_orders_completed", "completion_rate", "total_revenue", "commission_earned", "average_order_value", "on_time_delivery_percentage", "orders_by_status", "daily_performance"], "title": "EmployeePerformanceSchema", "type": "object"}}, "securitySchemes": {"AuthBearer": {"type": "http", "scheme": "bearer"}}}, "servers": []}