import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/office_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/models/user_model.dart';

// Order handling status enum based on API
enum OrderHandlingStatus { pending, assigned, processing, completed }

extension OrderHandlingStatusExtension on OrderHandlingStatus {
  String get name {
    switch (this) {
      case OrderHandlingStatus.pending:
        return 'PENDING';
      case OrderHandlingStatus.assigned:
        return 'ASSIGNED';
      case OrderHandlingStatus.processing:
        return 'PROCESSING';
      case OrderHandlingStatus.completed:
        return 'COMPLETED';
    }
  }

  String get displayName {
    switch (this) {
      case OrderHandlingStatus.pending:
        return 'في الانتظار';
      case OrderHandlingStatus.assigned:
        return 'مُعيَّن';
      case OrderHandlingStatus.processing:
        return 'قيد المعالجة';
      case OrderHandlingStatus.completed:
        return 'مكتمل';
    }
  }

  Color get color {
    switch (this) {
      case OrderHandlingStatus.pending:
        return AppColors.pending;
      case OrderHandlingStatus.assigned:
        return AppColors.assigned;
      case OrderHandlingStatus.processing:
        return AppColors.processing;
      case OrderHandlingStatus.completed:
        return AppColors.completed;
    }
  }

  static OrderHandlingStatus fromString(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return OrderHandlingStatus.pending;
      case 'ASSIGNED':
        return OrderHandlingStatus.assigned;
      case 'PROCESSING':
        return OrderHandlingStatus.processing;
      case 'COMPLETED':
        return OrderHandlingStatus.completed;
      default:
        return OrderHandlingStatus.pending;
    }
  }
}

// Order delivery status model
class OrderDeliveryStatus {
  final int? id;
  final OfficeModel office;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final OrderHandlingStatus? orderDefaultHandlingStatus;

  OrderDeliveryStatus({
    this.id,
    required this.office,
    required this.name,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.orderDefaultHandlingStatus,
  });

  factory OrderDeliveryStatus.fromJson(Map<String, dynamic> json) {
    return OrderDeliveryStatus(
      id: json['id'],
      office: OfficeModel.fromJson(json['office']),
      name: json['name'] ?? '',
      description: json['description'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      orderDefaultHandlingStatus:
          json['order_default_handling_status'] != null
              ? OrderHandlingStatusExtension.fromString(
                json['order_default_handling_status'],
              )
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'name': name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'order_default_handling_status': orderDefaultHandlingStatus?.name,
    };
  }
}

// Main Order model based on API schema
class OrderModelNew {
  final int? id;
  final OfficeModel office;
  final String code;
  final String? notes;
  final double? totalPrice;
  final String customerName;
  final String customerPhone;
  final String? customerAddress;
  final DateTime? deliveryDeadlineDate;
  final double? deliveryCustomerPayment;
  final OrderHandlingStatus orderHandlingStatus;
  final OrderDeliveryStatus? orderDeliveryStatus;
  final UserModel? assignedTo;
  final DateTime? assignedAt;
  final CompanyModel? customerCompany;
  final bool breakable;
  final DateTime createdAt;
  final DateTime updatedAt;

  OrderModelNew({
    this.id,
    required this.office,
    required this.code,
    this.notes,
    this.totalPrice,
    required this.customerName,
    required this.customerPhone,
    this.customerAddress,
    this.deliveryDeadlineDate,
    this.deliveryCustomerPayment,
    this.orderHandlingStatus = OrderHandlingStatus.pending,
    this.orderDeliveryStatus,
    this.assignedTo,
    this.assignedAt,
    this.customerCompany,
    this.breakable = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OrderModelNew.fromJson(Map<String, dynamic> json) {
    return OrderModelNew(
      id: json['id'],
      office: OfficeModel.fromJson(json['office']),
      code: json['code'] ?? '',
      notes: json['notes'],
      totalPrice:
          json['total_price'] != null
              ? double.tryParse(json['total_price'].toString())
              : null,
      customerName: json['customer_name'] ?? '',
      customerPhone: json['customer_phone'] ?? '',
      customerAddress: json['customer_address'],
      deliveryDeadlineDate:
          json['delivery_deadline_date'] != null
              ? DateTime.parse(json['delivery_deadline_date'])
              : null,
      deliveryCustomerPayment:
          json['delivery_customer_payment'] != null
              ? double.tryParse(json['delivery_customer_payment'].toString())
              : null,
      orderHandlingStatus: OrderHandlingStatusExtension.fromString(
        json['order_handling_status'] ?? 'PENDING',
      ),
      orderDeliveryStatus:
          json['order_delivery_status'] != null
              ? OrderDeliveryStatus.fromJson(json['order_delivery_status'])
              : null,
      assignedTo:
          json['assigned_to'] != null
              ? UserModel.fromJson(json['assigned_to'])
              : null,
      assignedAt:
          json['assigned_at'] != null
              ? DateTime.parse(json['assigned_at'])
              : null,
      customerCompany:
          json['customer_company'] != null
              ? CompanyModel.fromJson(json['customer_company'])
              : null,
      breakable: json['breakable'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'office': office.toJson(),
      'code': code,
      'notes': notes,
      'total_price': totalPrice,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'customer_address': customerAddress,
      'delivery_deadline_date': deliveryDeadlineDate?.toIso8601String(),
      'delivery_customer_payment': deliveryCustomerPayment,
      'order_handling_status': orderHandlingStatus.name,
      'order_delivery_status': orderDeliveryStatus?.toJson(),
      'assigned_to': assignedTo?.toJson(),
      'assigned_at': assignedAt?.toIso8601String(),
      'customer_company': customerCompany?.toJson(),
      'breakable': breakable,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  bool get isPending => orderHandlingStatus == OrderHandlingStatus.pending;
  bool get isAssigned => orderHandlingStatus == OrderHandlingStatus.assigned;
  bool get isProcessing =>
      orderHandlingStatus == OrderHandlingStatus.processing;
  bool get isCompleted => orderHandlingStatus == OrderHandlingStatus.completed;
  bool get hasAssignee => assignedTo != null;
  bool get hasDeadline => deliveryDeadlineDate != null;
  bool get isOverdue =>
      hasDeadline &&
      deliveryDeadlineDate!.isBefore(DateTime.now()) &&
      !isCompleted;

  @override
  String toString() {
    return 'OrderModelNew(id: $id, code: $code, customerName: $customerName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModelNew && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Request models for API operations
class OrderCreateRequest {
  final String code;
  final String? notes;
  final double? totalPrice;
  final String customerName;
  final String customerPhone;
  final String? customerAddress;
  final int customerCompany;
  final DateTime? deliveryDeadlineDate;
  final int? orderDeliveryStatus;
  final bool breakable;

  OrderCreateRequest({
    required this.code,
    this.notes,
    this.totalPrice,
    required this.customerName,
    required this.customerPhone,
    this.customerAddress,
    required this.customerCompany,
    this.deliveryDeadlineDate,
    this.orderDeliveryStatus,
    this.breakable = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'notes': notes,
      'total_price': totalPrice,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'customer_address': customerAddress,
      'customer_company': customerCompany,
      'delivery_deadline_date': deliveryDeadlineDate?.toIso8601String(),
      'order_delivery_status': orderDeliveryStatus,
      'breakable': breakable,
    };
  }
}

class OrderAssignRequest {
  final int employeeId;
  final double? specialCommissionRate;

  OrderAssignRequest({required this.employeeId, this.specialCommissionRate});

  Map<String, dynamic> toJson() {
    return {
      'employee_id': employeeId,
      'special_commission_rate': specialCommissionRate,
    };
  }
}

class OrderCompleteRequest {
  final String? proofType;
  final String? proofUrl;
  final double? latitude;
  final double? longitude;
  final double? deliveryCustomerPayment;
  final int? orderDeliveryStatus;

  OrderCompleteRequest({
    this.proofType,
    this.proofUrl,
    this.latitude,
    this.longitude,
    this.deliveryCustomerPayment,
    this.orderDeliveryStatus,
  });

  Map<String, dynamic> toJson() {
    return {
      'proof_type': proofType,
      'proof_url': proofUrl,
      'latitude': latitude,
      'longitude': longitude,
      'delivery_customer_payment': deliveryCustomerPayment,
      'order_delivery_status': orderDeliveryStatus,
    };
  }
}
