import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/order_history_models.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class OrderDetailsController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();

  // Constructor parameter
  final int orderId;

  // Reactive variables
  final Rx<OrderWithHistory?> _orderWithHistory = Rx<OrderWithHistory?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  OrderDetailsController({required this.orderId});

  // Getters
  OrderWithHistory? get orderWithHistory => _orderWithHistory.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;

  // Permission getters
  bool get canTransferOrder => _authService.hasPermission(UserRole.manager);
  bool get canChangeStatus => _authService.hasPermission(UserRole.manager);
  bool get canEditOrder => _authService.hasPermission(UserRole.manager);
  bool get canDeleteOrder => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    loadOrderDetails(orderId);
  }

  // Load order details with history
  Future<void> loadOrderDetails(int orderId) async {
    _isLoading.value = true;
    _error.value = '';

    final response = await _orderService.getOrderWithHistory(orderId);

    if (response.success && response.data != null) {
      _orderWithHistory.value = response.data!;
    } else {
      _error.value = response.message ?? 'فشل في جلب تفاصيل الطلب';
      Get.snackbar(
        'خطأ',
        _error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Refresh order details
  Future<void> refreshOrderDetails() async {
    if (_orderWithHistory.value?.order.id != null) {
      await loadOrderDetails(_orderWithHistory.value!.order.id!);
    }
  }

  // Placeholder methods for future functionality
  void transferOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة نقل الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Implement transfer order functionality
  }

  void changeOrderStatus() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة تغيير حالة الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Implement change order status functionality
  }

  void editOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة تعديل الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to edit order page
  }

  void deleteOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة حذف الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Implement delete order functionality
  }

  void assignOrder() {
    Get.snackbar(
      'قريباً',
      'سيتم تطوير ميزة تعيين الطلب قريباً',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    // TODO: Navigate to order assignment page
  }

  // Helper methods for timeline data
  List<TimelineEvent> getTimelineEvents() {
    if (_orderWithHistory.value == null) return [];

    final events = <TimelineEvent>[];
    final order = _orderWithHistory.value!.order;

    // Add order creation event
    events.add(
      TimelineEvent(
        title: 'تم إنشاء الطلب',
        description: 'تم إنشاء الطلب #${order.code}',
        timestamp: order.createdAt,
        type: TimelineEventType.created,
        icon: Icons.add_circle,
      ),
    );

    // Add assignment history events
    for (final assignment in _orderWithHistory.value!.assigningHistory) {
      events.add(
        TimelineEvent(
          title: 'تم تعيين الطلب',
          description:
              'تم تعيين الطلب إلى ${assignment.assignedTo.fullName} بواسطة ${assignment.assignedBy.fullName}',
          timestamp: assignment.assignedAt,
          type: TimelineEventType.assigned,
          icon: Icons.person_add,
        ),
      );
    }

    // Add status history events
    for (final statusChange in _orderWithHistory.value!.handlingStatusHistory) {
      events.add(
        TimelineEvent(
          title: 'تم تغيير الحالة',
          description:
              statusChange.handlingStatus != null
                  ? 'تم تغيير حالة المعالجة إلى ${statusChange.handlingStatus!.displayName}'
                  : 'تم تحديث حالة الطلب',
          timestamp: statusChange.createdAt,
          type: TimelineEventType.statusChanged,
          icon: Icons.update,
          note: statusChange.note,
        ),
      );
    }

    // Add proof events
    for (final proof in _orderWithHistory.value!.proofs) {
      events.add(
        TimelineEvent(
          title: 'تم إضافة إثبات',
          description:
              '${proof.proofType.displayName} بواسطة ${proof.proofBy.fullName}',
          timestamp: proof.createdAt,
          type: TimelineEventType.proofAdded,
          icon: Icons.camera_alt,
          hasImage: proof.hasImage,
          hasLocation: proof.hasLocation,
        ),
      );
    }

    // Sort events by timestamp (newest first)
    events.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return events;
  }
}

// Timeline event model for UI
class TimelineEvent {
  final String title;
  final String description;
  final DateTime timestamp;
  final TimelineEventType type;
  final IconData icon;
  final String? note;
  final bool hasImage;
  final bool hasLocation;

  TimelineEvent({
    required this.title,
    required this.description,
    required this.timestamp,
    required this.type,
    required this.icon,
    this.note,
    this.hasImage = false,
    this.hasLocation = false,
  });
}

enum TimelineEventType { created, assigned, statusChanged, proofAdded }

extension TimelineEventTypeExtension on TimelineEventType {
  Color get color {
    switch (this) {
      case TimelineEventType.created:
        return Colors.blue;
      case TimelineEventType.assigned:
        return Colors.orange;
      case TimelineEventType.statusChanged:
        return Colors.purple;
      case TimelineEventType.proofAdded:
        return Colors.green;
    }
  }
}
