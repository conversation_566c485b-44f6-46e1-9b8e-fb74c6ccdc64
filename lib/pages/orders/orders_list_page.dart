import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/orders/orders_list_controller.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/pages/orders/order_create_page.dart';
import 'package:myrunway/pages/orders/order_edit_page.dart';

class OrdersListPage extends StatelessWidget {
  const OrdersListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final OrdersListController controller = Get.put(OrdersListController());

    return Scaffold(
      appBar: AppBar(
        title: Text(controller.canViewAllOrders ? 'إدارة الطلبات' : 'طلباتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (controller.canCreateOrders)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => Get.to(() => const OrderCreatePage()),
            ),
        ],
      ),
      body: Obx(() {
        final orders =
            controller.canViewAllOrders
                ? controller.orders
                : controller.myOrders;

        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (orders.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  controller.canViewAllOrders
                      ? 'لا توجد طلبات'
                      : 'لا توجد طلبات مُعيَّنة لك',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                if (controller.canCreateOrders)
                  ElevatedButton.icon(
                    onPressed: () => Get.to(() => const OrderCreatePage()),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة طلب جديد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshOrders,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return _OrderCard(order: order, controller: controller);
            },
          ),
        );
      }),
    );
  }
}

class _OrderCard extends StatelessWidget {
  final OrderModelNew order;
  final OrdersListController controller;

  const _OrderCard({required this.order, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => controller.selectOrder(order),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'طلب #${order.code}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: order.orderHandlingStatus.color,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      order.orderHandlingStatus.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Customer info
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      order.customerName,
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // Phone
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    order.customerPhone,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),

              // Company
              if (order.customerCompany != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.business, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      order.customerCompany!.name,
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ],

              // Total price
              if (order.totalPrice != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${order.totalPrice!.toStringAsFixed(2)} ريال',
                      style: TextStyle(
                        color: Colors.green[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],

              // Assigned to
              if (order.assignedTo != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.person_pin, size: 16, color: Colors.blue[600]),
                    const SizedBox(width: 4),
                    Text(
                      'مُعيَّن إلى: ${order.assignedTo!.fullName}',
                      style: TextStyle(color: Colors.blue[600], fontSize: 14),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    'تاريخ الإنشاء: ${_formatDate(order.createdAt)}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  const Spacer(),
                  if (controller.canEditOrders)
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed:
                          () => Get.to(() => OrderEditPage(order: order)),
                      color: AppColors.primary,
                    ),
                  if (controller.canDeleteOrders)
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20),
                      onPressed: () => _showDeleteDialog(context),
                      color: Colors.red,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الطلب "#${order.code}"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Get.back();
              if (order.id != null) {
                controller.deleteOrder(order.id!);
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
