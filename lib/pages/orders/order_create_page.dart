import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/orders/order_create_controller.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/utils/form_validators.dart';

class OrderCreatePage extends StatefulWidget {
  const OrderCreatePage({super.key});

  @override
  State<OrderCreatePage> createState() => _OrderCreatePageState();
}

class _OrderCreatePageState extends State<OrderCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  final _notesController = TextEditingController();
  final _totalPriceController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();

  final OrderCreateController _controller = Get.put(OrderCreateController());

  int? _selectedCompanyId;
  DateTime? _deliveryDeadline;
  bool _isBreakable = false;

  @override
  void dispose() {
    _codeController.dispose();
    _notesController.dispose();
    _totalPriceController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة طلب جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Order Code
              TextFormField(
                controller: _codeController,
                decoration: const InputDecoration(
                  labelText: 'رمز الطلب *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.qr_code),
                ),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Customer Name
              TextFormField(
                controller: _customerNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العميل *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Customer Phone
              TextFormField(
                controller: _customerPhoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم هاتف العميل *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                validator: FormValidators.phone,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Company Selection
              Obx(
                () => DropdownButtonFormField<int>(
                  value: _selectedCompanyId,
                  decoration: const InputDecoration(
                    labelText: 'الشركة *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.business),
                  ),
                  items:
                      _controller.companies.map((company) {
                        return DropdownMenuItem<int>(
                          value: company.id,
                          child: Text(company.name),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCompanyId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الشركة';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 16),

              // Customer Address
              TextFormField(
                controller: _customerAddressController,
                decoration: const InputDecoration(
                  labelText: 'عنوان العميل',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                maxLines: 3,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Total Price
              TextFormField(
                controller: _totalPriceController,
                decoration: const InputDecoration(
                  labelText: 'إجمالي السعر',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: 'ريال',
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.validateAmount(value);
                  }
                  return null;
                },
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Delivery Deadline
              InkWell(
                onTap: _selectDeliveryDeadline,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'موعد التسليم',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _deliveryDeadline != null
                        ? '${_deliveryDeadline!.day}/${_deliveryDeadline!.month}/${_deliveryDeadline!.year}'
                        : 'اختر موعد التسليم',
                    style: TextStyle(
                      color:
                          _deliveryDeadline != null
                              ? Colors.black87
                              : Colors.grey[600],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Breakable checkbox
              CheckboxListTile(
                title: const Text('يحتوي على أشياء قابلة للكسر'),
                subtitle: const Text('مثل الزجاج أو الأشياء الهشة'),
                value: _isBreakable,
                onChanged: (value) {
                  setState(() {
                    _isBreakable = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              const SizedBox(height: 16),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: 32),

              // Create Button
              Obx(
                () => ElevatedButton(
                  onPressed: _controller.isCreating ? null : _createOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _controller.isCreating
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'إنشاء الطلب',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDeliveryDeadline() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _deliveryDeadline = picked;
      });
    }
  }

  Future<void> _createOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = OrderCreateRequest(
      code: _codeController.text.trim(),
      customerName: _customerNameController.text.trim(),
      customerPhone: _customerPhoneController.text.trim(),
      customerCompany: _selectedCompanyId!,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
      totalPrice:
          _totalPriceController.text.trim().isEmpty
              ? null
              : double.parse(_totalPriceController.text.trim()),
      customerAddress:
          _customerAddressController.text.trim().isEmpty
              ? null
              : _customerAddressController.text.trim(),
      deliveryDeadlineDate: _deliveryDeadline,
      breakable: _isBreakable,
    );

    final success = await _controller.createOrder(request);
    if (success) {
      Get.back(closeOverlays: true);
    }
  }
}
